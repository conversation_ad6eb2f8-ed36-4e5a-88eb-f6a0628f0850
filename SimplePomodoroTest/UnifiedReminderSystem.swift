//
//  UnifiedReminderSystem.swift
//  uProd
//
//  Единая система напоминаний для всех типов интервалов
//

import Foundation
import Cocoa

// УПРОЩЕНО: Используем String вместо enum для избежания проблем с областью видимости

/// Единая система напоминаний для формальных и неформальных интервалов
class UnifiedReminderSystem {
    
    // MARK: - Singleton
    static let shared = UnifiedReminderSystem()
    private init() {}
    
    // MARK: - Properties
    private var escalationTimer: Timer?
    private var escalationStartTime: Date?
    private var lastEscalationLevel: Int = -1
    private var isTestMode: Bool = false
    private var currentIntervalType: String = "informal" // Тип текущего интервала
    
    // Callbacks для интеграции с AppDelegate
    weak var delegate: UnifiedReminderSystemDelegate?
    
    // MARK: - Public Methods
    
    /// Запускает систему эскалации для любого типа интервала
    func startEscalation(for intervalType: String, isTest: Bool = false) {
        // ПРОСТЕЙШИЙ ТЕСТ: Только logInfo
        logInfo("ТЕСТ", "МЕТОД ВЫЗВАН!")

        // КРИТИЧЕСКИЙ ТЕСТ: Этот print должен появиться в логах!
        print("🚨 КРИТИЧЕСКИЙ ТЕСТ: startEscalation ВЫЗВАН!")
        logInfo("UnifiedReminder", "🚨 КРИТИЧЕСКИЙ ТЕСТ: startEscalation ВЫЗВАН!")
        print("🚨 КРИТИЧЕСКИЙ ТЕСТ: intervalType = \(intervalType), isTest = \(isTest)")
        logInfo("UnifiedReminder", "🚨 КРИТИЧЕСКИЙ ТЕСТ: intervalType = \(intervalType), isTest = \(isTest)")

        print("🚀 UnifiedReminderSystem: НОВЫЙ метод startEscalation вызван с параметрами: \(intervalType), \(isTest)")
        print("🎯 UnifiedReminderSystem: Запуск единой системы эскалации для \(intervalType) (тест: \(isTest))")
        logInfo("UnifiedReminder", "🎯 Запуск единой системы эскалации для \(intervalType) (тест: \(isTest))")

        logInfo("UnifiedReminder", "🔍 DEBUG: Шаг 1 - Начало метода startEscalation")
        print("🔍 UnifiedReminderSystem: DEBUG: Шаг 1 - Начало метода startEscalation")

        print("🔍 UnifiedReminderSystem: DEBUG: Шаг 1 - Установка currentIntervalType")
        currentIntervalType = intervalType

        print("🔍 UnifiedReminderSystem: DEBUG: Шаг 2 - Установка isTestMode")
        isTestMode = isTest

        print("🔍 UnifiedReminderSystem: DEBUG: Шаг 3 - Установка escalationStartTime")
        escalationStartTime = Date()

        print("🔍 UnifiedReminderSystem: DEBUG: Шаг 4 - Установка lastEscalationLevel")
        lastEscalationLevel = -1

        print("🔍 UnifiedReminderSystem: DEBUG: Шаг 5 - Создание Timer")
        logInfo("UnifiedReminder", "🔍 DEBUG: Шаг 5 - Создание Timer")

        // Запускаем таймер с интервалом 1 секунда для точной эскалации
        escalationTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.logInfo("UnifiedReminder", "⏰ Timer callback вызван")
            self?.checkEscalation()
        }

        logInfo("UnifiedReminder", "🔍 DEBUG: Timer создан: \(escalationTimer != nil)")

        if let timer = escalationTimer {
            logInfo("UnifiedReminder", "🔍 DEBUG: Timer valid: \(timer.isValid)")
        }

        print("✅ UnifiedReminderSystem: Единая система эскалации запущена для \(intervalType)")
        logInfo("UnifiedReminder", "✅ Единая система эскалации запущена для \(intervalType)")
    }

    /// Обратная совместимость - по умолчанию неформальный интервал
    func startEscalation(isTest: Bool = false) {
        print("🔄 UnifiedReminderSystem: СТАРЫЙ метод startEscalation вызван!")
        startEscalation(for: "informal", isTest: isTest)
    }
    
    /// Останавливает систему эскалации
    func stopEscalation() {
        logInfo("UnifiedReminder", "🛑 Остановка единой системы эскалации")
        
        escalationTimer?.invalidate()
        escalationTimer = nil
        escalationStartTime = nil
        lastEscalationLevel = -1
        isTestMode = false
        
        logInfo("UnifiedReminder", "✅ Единая система эскалации остановлена")
    }
    
    /// Проверяет нужно ли показать следующий уровень эскалации
    private func checkEscalation() {
        logInfo("UnifiedReminder", "🔍 DEBUG: checkEscalation() вызван")

        guard let startTime = escalationStartTime else {
            logInfo("UnifiedReminder", "🔍 DEBUG: escalationStartTime is nil, возвращаемся")
            return
        }

        logInfo("UnifiedReminder", "🔍 DEBUG: Вычисляем elapsedSeconds")
        let elapsedSeconds = Date().timeIntervalSince(startTime)
        let elapsedMinutes = Int(elapsedSeconds / 60)

        logInfo("UnifiedReminder", "🔍 DEBUG: elapsedSeconds = \(elapsedSeconds), elapsedMinutes = \(elapsedMinutes)")
        
        // В режиме теста НЕ проверяем активность пользователя
        let shouldContinue: Bool
        if isTestMode {
            shouldContinue = true
        } else {
            // В обычном режиме проверяем активность через единую систему
            shouldContinue = UnifiedActivityChecker.shared.isUserCurrentlyActive()
            if !shouldContinue {
                logInfo("UnifiedReminder", "Пользователь неактивен - останавливаем эскалацию")
                stopEscalation()
                return
            }
        }
        
        // Получаем текущий уровень эскалации из OvertimeConfig
        let currentLevel = OvertimeConfig.getLevelNumber(for: elapsedMinutes)

        // Показываем напоминание только при изменении уровня
        if currentLevel != lastEscalationLevel {
            lastEscalationLevel = currentLevel
            logInfo("UnifiedReminder", "📈 Изменение уровня эскалации: \(elapsedMinutes) минут = уровень \(currentLevel)")

            // Уведомляем делегата о необходимости показать напоминание
            delegate?.showEscalationReminder(minutes: elapsedMinutes, level: currentLevel, for: currentIntervalType)

            // Обновляем статус-бар
            delegate?.updateStatusBar(minutes: elapsedMinutes)
        }
    }
    
    // MARK: - Utility Methods
    
    /// Получает текущее состояние системы для отладки
    func getDebugInfo() -> String {
        let isActive = escalationTimer != nil
        let elapsed = escalationStartTime.map { Date().timeIntervalSince($0) } ?? 0
        let minutes = Int(elapsed / 60)
        
        return """
        🔍 Единая система эскалации:
        • Активна: \(isActive)
        • Режим теста: \(isTestMode)
        • Прошло времени: \(minutes) минут
        • Текущий уровень: \(lastEscalationLevel)
        • Следующая проверка: через \(60 - Int(elapsed.truncatingRemainder(dividingBy: 60))) сек
        """
    }
    
    private func logInfo(_ category: String, _ message: String) {
        print("[\(category)] \(message)")
        // Можно добавить интеграцию с системой логирования приложения
    }
}

// MARK: - Delegate Protocol

/// Протокол для интеграции UnifiedReminderSystem с AppDelegate
protocol UnifiedReminderSystemDelegate: AnyObject {

    /// Показать напоминание определенного уровня
    func showEscalationReminder(minutes: Int, level: Int, for intervalType: String)

    /// Обновить статус-бар с информацией о переработке
    func updateStatusBar(minutes: Int)
}


