# 🧪 Документация по тестированию uProd

## 📋 Обзор системы тестирования

uProd использует **автоматическую модульную систему тестирования** с реальной логикой для обеспечения максимального качества и надежности приложения.

## 🎯 Принципы тестирования

### **⚠️ КРИТИЧЕСКИ ВАЖНО: Только реальная логика!**

**✅ ПРАВИЛЬНО:**
- Тестировать **РЕАЛЬНЫЕ** классы и методы приложения
- Использовать **настоящую логику** InformalSessionDetector, PomodoroTimer, etc.
- Изолировать только **внешние зависимости** (UI, файлы, сеть, таймеры)
- Тесты должны быть **максимально близки к реальности**

**❌ НЕПРАВИЛЬНО:**
- Создавать упрощенные версии основной логики
- Использовать "TestableXXX" классы вместо реальных
- Мокать основную бизнес-логику
- Создавать "игрушечные" тесты ради тестов

### **🔍 Философия: "Тесты как страховка"**
- Тесты должны **реально защищать** от регрессий
- Если тест проходит, но реальный код сломан - тест бесполезен
- Лучше **меньше тестов, но качественных**, чем много поверхностных
- Каждый тест должен **находить реальные проблемы**

## 🎯 **КРИТИЧЕСКИ ВАЖНО: Типы тестов и защита от регрессий**

### **🚨 ГЛАВНАЯ ОПАСНОСТЬ: Mock-тесты = ИЛЛЮЗИЯ БЕЗОПАСНОСТИ!**

**Реальная проблема Mock-тестов:**
- ✅ **Mock-тесты проходят** - показывают "все хорошо"
- ❌ **Реальный код сломан** - приложение не работает
- 🐛 **Проблема:** Mock-тесты тестируют СВОЮ логику, а не реальную
- 💥 **Результат:** Ложная уверенность в качестве кода

### **⚠️ ОПАСНОСТЬ: Только Mock-тесты = НЕТ ЗАЩИТЫ ОТ РЕГРЕССИЙ!**

**Реальный эксперимент из проекта:**
1. **Сломали реальную логику** в AppDelegate (окно не показывается)
2. **Mock-тесты прошли** ✅ (5/5) - показали "все хорошо"
3. **Реальные тесты упали** ❌ - поймали проблему
4. **Вывод:** Mock-тесты НЕ защищают от регрессий!

### **🔬 ТИП 1: MOCK-ТЕСТЫ (НЕ РЕКОМЕНДУЕТСЯ для защиты от регрессий)**
- **Что тестируют:** Свою собственную Mock-логику
- **Пример:** MockInformalSessionDetector с MockWindowManager
- **Цель:** Проверить алгоритмы в изоляции
- **Скорость:** Очень быстрые (<0.001с)
- **⚠️ ПРОБЛЕМА:** НЕ защищают от поломок реального кода
- **Когда использовать:** Только для тестирования чистых алгоритмов

### **✅ ТИП 2: РЕАЛЬНЫЕ ТЕСТЫ (РЕКОМЕНДУЕТСЯ для защиты от регрессий)**
- **Что тестируют:** Реальный код приложения
- **Пример:** Читают AppDelegate.swift и проверяют логику
- **Цель:** Поймать реальные поломки в коде
- **Скорость:** Быстрые (<1с)
- **✅ ПРЕИМУЩЕСТВО:** РЕАЛЬНО защищают от регрессий
- **Когда использовать:** ВСЕГДА для критической логики

### **🔗 ТИП 3: ИНТЕГРАЦИОННЫЕ ТЕСТЫ (ОБЯЗАТЕЛЬНО)**
- **Что тестируют:** Полный путь от входа до результата
- **Пример:** От `recordMinuteActivity()` до показа окна пользователю
- **Цель:** Проверить что компоненты работают ВМЕСТЕ
- **Скорость:** Быстрые (<1с)
- **Когда обязательны:** Для критической логики с несколькими компонентами

## 🎯 **ПРИНЦИП: ОДИН ТЕСТ НА ФУНКЦИЮ**

### **📋 ПРАВИЛО: Для каждой функции создавай ТОЛЬКО ОДИН главный тест!**

**✅ ПРАВИЛЬНО:**
- **InformalSessionTest.swift** - тест неформальных сессий
- **PomodoroTimerTest.swift** - тест помодоро таймера
- **RealAppDelegateLogicTest.swift** - тест логики показа окна
- **BreakSystemTest.swift** - тест системы отдыха

**❌ НЕПРАВИЛЬНО:**
- 10+ тестов для одной функции показа окна
- WindowTest1, WindowTest2, WindowTest3...
- Дублирование тестов с разными названиями

### **🎯 ПРИОРИТЕТЫ ТЕСТИРОВАНИЯ (в порядке важности)**

#### **1️⃣ ВЫСШИЙ ПРИОРИТЕТ: Реальные тесты**
- **Цель:** Защита от регрессий в реальном коде
- **Что делать:** Читать исходный код и проверять логику
- **Пример:** `Tests/RealAppDelegateLogicTest.swift`
- **⚠️ БЕЗ ЭТОГО:** Нет защиты от поломок!

#### **2️⃣ ВЫСОКИЙ ПРИОРИТЕТ: Интеграционные тесты**
- **Цель:** Проверить что компоненты работают вместе
- **Что делать:** Тестировать полный путь от входа до результата
- **Пример:** От записи активности до показа окна
- **⚠️ БЕЗ ЭТОГО:** Компоненты могут не работать вместе!

#### **3️⃣ СРЕДНИЙ ПРИОРИТЕТ: Mock-тесты алгоритмов**
- **Цель:** Проверить чистые алгоритмы и математику
- **Что делать:** Тестировать логику в изоляции
- **Пример:** Подсчет активных минут
- **⚠️ ВНИМАНИЕ:** НЕ защищают от регрессий в реальном коде!

### **📋 ПРАВИЛО: Начинай с реального теста, НЕ создавай дубликаты!**

## 🧪 **Практические рекомендации**

### **🚀 Как создавать качественные тесты (правильный порядок):**

**1. СНАЧАЛА: Реальный тест (защита от регрессий):**
```swift
// ✅ ПРАВИЛЬНО: Проверяем реальный код
func testRealAppDelegateLogic() {
    let content = try! String(contentsOfFile: "AppDelegate.swift")

    // Проверяем что нет сломанной логики
    let hasBrokenLogic = content.contains("🐛 ВРЕМЕННО СЛОМАННАЯ ЛОГИКА")
    assert(!hasBrokenLogic, "Обнаружена сломанная логика!")

    // Проверяем что есть правильная логика
    let hasCorrectLogic = content.contains("existingWindow.isVisible")
    assert(hasCorrectLogic, "Правильная логика не найдена!")
}
```

**2. ПОТОМ: Интеграционный тест (полный путь):**
```swift
// ✅ ПРАВИЛЬНО: Тестируем полный путь
func testFullWorkflow() {
    // Полный путь от входа до результата
    detector.recordActivity(true)    // Вход
    detector.checkConditions()       // Обработка
    let result = app.showWindow()    // Результат
    assert(result == true)           // Проверка
}
```

**3. В КОНЦЕ: Mock-тесты (только для алгоритмов):**
```swift
// ⚠️ ОСТОРОЖНО: НЕ защищает от регрессий!
func testMockCalculation() {
    let mockDetector = MockDetector()
    let result = mockDetector.calculate([true, false])
    assert(result == 1)  // Тестирует только Mock-логику
}
```

**3. Тестируй реальные сценарии:**
- ✅ "Пользователь работал 55 минут - должно показаться окно"
- ❌ "Функция возвращает true при входе X"

**4. Проверяй граничные случаи:**
- Ровно на границе срабатывания (42 минуты)
- Чуть меньше границы (41 минута)
- Недостаток данных (30 минут)

### **⚠️ Типичные ошибки и как их избежать:**

**❌ ОШИБКА 1: "Тестирую только логику"**
```swift
// Плохо - тестирует только математику
func testShouldTrigger() {
    assert(detector.shouldTrigger() == true)
}
```
**✅ РЕШЕНИЕ: Добавь интеграционный тест**
```swift
// Хорошо - тестирует полный сценарий
func testUserWorked55Minutes() {
    // Симулируем реальную работу пользователя
    for _ in 1...55 { detector.recordActivity(true) }
    // Проверяем что система РЕАЛЬНО сработает
    assert(detector.wouldShowWindow() == true)
}
```

**❌ ОШИБКА 2: "Тесты проходят, но приложение не работает"**
- **Причина:** Тестируешь изолированные компоненты, но не их взаимодействие
- **Решение:** Создай тест полного пути от входа до результата

**❌ ОШИБКА 3: "Мокаю всё подряд"**
- **Причина:** Боишься медленных тестов
- **Решение:** Мокай только внешние зависимости (UI, файлы, сеть)

## 🏗️ Архитектура тестов

### **Уровень 1: Автоматические юнит-тесты (командная строка)**
- **Запускаются при каждой сборке** через `./build.sh`
- **Тестируют реальную логику** приложения без упрощений
- **Быстрые** (<1 секунды) и **детерминированные**
- **Останавливают сборку** при обнаружении проблем
- **Модульная структура** - каждый компонент отдельно

### **Уровень 2: Интеграционные тесты (через интерфейс)**
- Доступны через меню "🧪 Другие тесты → 🤖 Автотест неформальных сессий"
- **Тестируют взаимодействие** компонентов в реальном приложении
- **Используют реальные классы** с минимальными моками
- Для **финальной проверки** перед релизом

### **Уровень 3: Ручные тесты (через интерфейс)**
- Для **отладки и демонстрации** конкретных сценариев
- **Визуальная проверка** UI компонентов
- Доступны через меню "🧪 Другие тесты"

## 📁 Структура тестов

```
Tests/
├── InformalSessionTest.swift       ✅ 6 тестов  - Неформальные сессии
├── PomodoroTimerTest.swift         ✅ 5 тестов  - Формальные интервалы
├── BreakSystemTest.swift           ✅ 6 тестов  - Система отдыха
├── WindowDisplayTest.swift         ✅ 4 теста   - Показ окна (с моками)
├── IntegratedWindowTest.swift      ✅ 4 теста   - Интегрированные тесты окна
├── WindowLogicTest.swift           ✅ 8 тестов  - Логика показа окна [КРИТИЧЕСКИЙ]
├── ActivityDetectionTest.swift     📋 Планируется - Детекция активности
├── SleepWakeTest.swift            📋 Планируется - Сон/пробуждение
├── ProjectManagementTest.swift    📋 Планируется - Управление проектами
├── StatisticsTest.swift           📋 Планируется - Статистика
└── IntegrationTest.swift          📋 Планируется - Интеграционные тесты
```

**📊 Быстрая навигация:**
- [📋 Детальная структура тестов](#-детальная-структура-тестов) - Полное описание всех тестов
- [📊 Сводная таблица](#-сводная-таблица-всех-тестов) - Общая статистика
- [🚀 Запуск тестов](#-запуск-тестов) - Как запускать тесты
- [💡 Примеры](#-примеры-правильного-и-неправильного-тестирования) - Правильное и неправильное тестирование

### **Модульная архитектура:**
- **Каждый модуль** - отдельный исполняемый Swift файл с реальной логикой
- **Независимые тесты** - можно запускать по отдельности для отладки
- **Единый test.sh** - запускает все модули автоматически при сборке
- **Расширяемость** - легко добавить новые модули по шаблону
- **Реальные классы** - используют RealInformalSessionDetector, TestablePomodoroTimer, etc.

## 📊 Текущее состояние (обновлено)

**✅ Реализовано и работает:**
- 🧪 **33 автоматических теста** в 6 модулях
- ⚡ **Интеграция в сборку** - тесты запускаются при `./build.sh`
- 🛡️ **Защита от регрессий** - сборка останавливается при провалах
- 🎯 **Реальная логика** - тесты используют настоящие классы приложения
- 📈 **100% успешность** на текущей версии
- ⭐ **Критическое исправление** - тест логики показа окна защищает от регрессии

**📋 Статистика:**
- Время выполнения: <1 секунды для всех тестов
- Покрытие: 6 основных компонентов (неформальные сессии, Pomodoro, отдых, показ окна)
- Найденные баги: 3 (порог срабатывания, автосброс активности, логика показа окна)
- Критические исправления: 1 (проблема с невидимыми окнами)

## 🚀 Запуск тестов

### Автоматический запуск при сборке
```bash
./build.sh
```
Тесты запускаются автоматически перед сборкой. Если тесты провалены, сборка останавливается.

### Ручной запуск всех тестов
```bash
./test.sh
```

### Запуск отдельного модуля
```bash
# Неформальные сессии
swift Tests/InformalSessionTest.swift

# Формальные интервалы
swift Tests/PomodoroTimerTest.swift

# Система отдыха
swift Tests/BreakSystemTest.swift
```

### Добавление нового модуля тестов

**🎯 Принципы создания новых тестов:**

1. **Используйте РЕАЛЬНЫЕ классы:**
   ```swift
   // ✅ ПРАВИЛЬНО
   let detector = InformalSessionDetector()

   // ❌ НЕПРАВИЛЬНО
   let detector = TestableInformalSessionDetector()
   ```

2. **Изолируйте только внешние зависимости:**
   ```swift
   // ✅ ПРАВИЛЬНО - мокаем UI/файлы/сеть
   let mockUI = MockUserInterface()
   let realDetector = InformalSessionDetector(ui: mockUI)

   // ❌ НЕПРАВИЛЬНО - мокаем основную логику
   let mockDetector = MockInformalSessionDetector()
   ```

3. **Тестируйте реальные сценарии:**
   ```swift
   // ✅ ПРАВИЛЬНО - реальный кейс пользователя
   for _ in 1...52 { detector.recordMinuteActivity(isActive: true) }

   // ❌ НЕПРАВИЛЬНО - искусственный тест
   detector.setActiveMinutes(52)
   ```

**📋 Пошаговая инструкция:**

1. **Скопируйте шаблон** из существующего теста
2. **Замените логику** на тестирование вашего компонента
3. **Используйте реальные классы** вместо упрощенных
4. **Сделайте исполняемым:** `chmod +x Tests/NewTest.swift`
5. **Добавьте в test.sh:** `run_test "Новый модуль" "Tests/NewTest.swift"`
6. **Проверьте:** `swift Tests/NewTest.swift`

## 💡 Примеры правильного и неправильного тестирования

### **✅ ПРАВИЛЬНЫЙ подход:**

```swift
// Тестируем РЕАЛЬНЫЙ InformalSessionDetector
func testRealInformalSession() {
    let detector = InformalSessionDetector() // Реальный класс!

    // Реальный сценарий пользователя
    for _ in 1...52 {
        detector.recordMinuteActivity(isActive: true)
    }

    // Проверяем реальное поведение
    let shouldTrigger = detector.shouldTriggerRestSuggestion()
    assert(shouldTrigger, "52 активных минуты должны вызвать предложение отдыха")
}
```

### **❌ НЕПРАВИЛЬНЫЙ подход:**

```swift
// НЕ создавайте упрощенные версии!
class TestableInformalSessionDetector {  // ❌ Плохо!
    var fakeActiveMinutes = 0

    func setActiveMinutes(_ count: Int) {  // ❌ Искусственно!
        fakeActiveMinutes = count
    }

    func shouldTriggerRestSuggestion() -> Bool {
        return fakeActiveMinutes >= 42  // ❌ Упрощенная логика!
    }
}

func testFakeDetector() {  // ❌ Бесполезный тест!
    let detector = TestableInformalSessionDetector()
    detector.setActiveMinutes(50)  // ❌ Не реальный сценарий!
    assert(detector.shouldTriggerRestSuggestion())  // ❌ Ничего не проверяет!
}
```

### **🎯 Почему реальная логика важна:**

1. **Находит реальные баги** - упрощенные тесты пропускают проблемы
2. **Защищает от регрессий** - изменения в коде сразу ломают тесты
3. **Документирует поведение** - тесты показывают как система работает
4. **Дает уверенность** - если тесты прошли, система реально работает

## 📋 Детальная структура тестов

### **🧪 Модуль 1: InformalSessionTest**
**Файл:** `Tests/InformalSessionTest.swift`
**Описание:** Тестирует логику срабатывания системы неформальных сессий

| № | Название теста | Сценарий | Ожидание | Цель |
|---|---|---|---|---|
| 1 | **Идеальная работа** | 52 активных минуты подряд | ✅ Сработать | Проверка базового срабатывания |
| 2 | **Работа с перерывами** | 45 активных + 7 неактивных | ✅ Сработать | Короткие перерывы не мешают |
| 3 | **Недостаточно активности** | 41 активная + 11 неактивных | ❌ НЕ сработать | Проверка порога 42 минуты |
| 4 | **Граничный случай** | Ровно 42 активных минуты | ✅ Сработать | Минимальный порог срабатывания |
| 5 | **Недостаточно данных** | Только 30 минут данных | ❌ НЕ сработать | Нужно минимум 52 минуты |
| 6 | **Сброс состояния** | Сброс → проверка пустого состояния | ❌ НЕ сработать | Корректность сброса |

**Критерии срабатывания:**
- 📊 **Минимум данных:** 52 минуты
- ⚡ **Минимум активности:** 42 минуты (≥80%)
- 🔄 **Автосброс:** При 15+ неактивных минутах подряд

---

### **⏰ Модуль 2: PomodoroTimerTest**
**Файл:** `Tests/PomodoroTimerTest.swift`
**Описание:** Тестирует логику формальных интервалов (Pomodoro Timer)

| № | Название теста | Сценарий | Ожидание | Цель |
|---|---|---|---|---|
| 1 | **Запуск рабочего интервала** | Вызов startWork() | ✅ Состояние = working | Корректный старт |
| 2 | **Переход на короткий перерыв** | startWork() → startShortBreak() | ✅ Состояние = shortBreak | Переход между состояниями |
| 3 | **Логика длинного перерыва** | 4 интервала → shouldTakeLongBreak() | ✅ true | Каждый 4-й интервал |
| 4 | **Пауза и возобновление** | startWork() → pause() → resume() | ✅ working → paused → working | Управление состоянием |
| 5 | **Остановка таймера** | startWork() → stop() | ✅ idle, счетчик = 0 | Полный сброс |

**Состояния таймера:**
- 🟢 **idle** - ожидание
- 🔴 **working** - рабочий интервал
- ⏸️ **paused** - пауза
- ☕ **shortBreak** - короткий перерыв (5 мин)
- 🛌 **longBreak** - длинный перерыв (15 мин)

---

### **☕ Модуль 3: BreakSystemTest**
**Файл:** `Tests/BreakSystemTest.swift`
**Описание:** Тестирует систему отслеживания качества отдыха

| № | Название теста | Сценарий | Ожидание | Цель |
|---|---|---|---|---|
| 1 | **Начало отслеживания** | startTracking() | ✅ isTracking = true | Активация системы |
| 2 | **Идеальный отдых** | Без записи активности | ✅ Качество = 100% | Полный покой |
| 3 | **Плохой отдых** | 10 активных записей | ✅ Качество = 0% | Постоянная активность |
| 4 | **Средний отдых** | 50% активности | ✅ Качество = 50% | Смешанная активность |
| 5 | **Остановка отслеживания** | stopTracking() | ✅ isTracking = false | Деактивация системы |
| 6 | **Изоляция данных** | Активность без отслеживания | ✅ Качество = 100% | Данные не записываются |

**Формула качества отдыха:**
```
Качество = 100% - (Активные минуты / Общие минуты) × 100%
```

---

## 📊 Сводная таблица всех тестов

| Модуль | Тестов | Компонент | Статус | Время выполнения |
|---|---|---|---|---|
| 🧪 **InformalSessionTest** | 6 | Неформальные сессии | ✅ 100% | <0.001с |
| ⏰ **PomodoroTimerTest** | 5 | Формальные интервалы | ✅ 100% | <0.001с |
| ☕ **BreakSystemTest** | 6 | Система отдыха | ✅ 100% | <0.001с |
| 📋 **Планируется...** | - | Детекция активности | 🔄 В разработке | - |
| 📋 **Планируется...** | - | Сон/пробуждение | 🔄 В разработке | - |
| 📋 **Планируется...** | - | Управление проектами | 🔄 В разработке | - |
| 📋 **Планируется...** | - | Статистика | 🔄 В разработке | - |

**📈 Общая статистика:**
- **Всего тестов:** 17 (6+5+6)
- **Покрытие:** 3 основных компонента
- **Успешность:** 100% (все тесты проходят)
- **Общее время:** <1 секунды
- **Найденные баги:** 2 (исправлены)

**🎯 Типы тестируемых сценариев:**
- ✅ **Позитивные** - когда система должна сработать
- ❌ **Негативные** - когда система НЕ должна сработать
- ⚖️ **Граничные** - пороговые значения
- 🔄 **Состояния** - переходы между состояниями
- 🧹 **Сброс** - очистка данных и состояний

### **Критерии срабатывания:**
- **Минимум данных:** 52 минуты
- **Минимум активности:** 42 минуты (80%)
- **Формальный таймер:** должен быть неактивен

## 🚀 Запуск тестов

### **Автоматический запуск через интерфейс (рекомендуется)**
1. Запустите приложение uProd
2. Откройте меню "🧪 Другие тесты"
3. Выберите "🤖 Автотест неформальных сессий"
4. Нажмите "Запустить"
5. Получите полный отчет с результатами

### **Ручной запуск через командную строку (для разработчиков)**
```bash
# Запуск всех тестов (требует настройки схемы)
xcodebuild test -project SimplePomodoroTest.xcodeproj -scheme SimplePomodoroTest -destination 'platform=macOS'
```

**Примечание:** Интеграция с командной строкой планируется в будущих версиях.

## 📊 Интерпретация результатов

### **Успешный запуск:**
```
Test Suite 'InformalSessionTests' passed at 2025-07-24 12:53:02.123.
	 Executed 8 tests, with 0 failures (0 unexpected) in 0.045 (0.047) seconds
✅ Все тесты прошли успешно!
```

### **Провал тестов:**
```
Test Case '-[SimplePomodoroTestTests.InformalSessionTests testIdealWork52Minutes]' failed (0.001 seconds).
❌ Тесты провалены! Сборка остановлена.
```

## 🔧 Добавление новых тестов

### **Создание нового модуля тестов:**

1. **Создайте файл** `NewModuleTests.swift` в папке `SimplePomodoroTestTests/`

2. **Используйте шаблон:**
```swift
import XCTest
@testable import uProd

class NewModuleTests: XCTestCase {
    
    override func setUp() {
        super.setUp()
        // Инициализация
    }
    
    override func tearDown() {
        // Очистка
        super.tearDown()
    }
    
    func testSpecificScenario() {
        // Arrange: Подготовка
        
        // Act: Действие
        
        // Assert: Проверка
        XCTAssertTrue(condition, "Описание ошибки")
    }
}
```

3. **Добавьте в build.sh:**
```bash
-only-testing:SimplePomodoroTestTests/NewModuleTests
```

### **Добавление теста в существующий модуль:**

1. Добавьте метод `func testNewScenario()` в класс
2. Следуйте паттерну Arrange-Act-Assert
3. Используйте описательные имена и сообщения

## 📋 Планируемые модули тестов

### **PomodoroTimerTests**
- Тестирование формальных интервалов
- Переходы между состояниями
- Обработка пауз и остановок

### **UnifiedReminderTests**
- Система окон и напоминаний
- Логика показа уведомлений
- Обработка пользовательских действий

### **ActivityDetectionTests**
- Определение активности пользователя
- Обработка событий мыши/клавиатуры
- Калибровка чувствительности

### **SleepWakeTests**
- Обработка сна/пробуждения системы
- Сброс счетчиков активности
- Восстановление состояния

### **BreakQualityTests**
- Система оценки качества отдыха
- Анализ активности во время перерыва
- Расчет рекомендаций

### **ProjectManagementTests**
- Создание, редактирование, удаление проектов
- Переключение между проектами
- Сохранение и загрузка данных

### **StatisticsTests**
- Подсчет статистики работы
- Агрегация данных по периодам
- Экспорт отчетов

### **IntegrationTests**
- Взаимодействие между компонентами
- End-to-end сценарии
- Тестирование полного workflow

## 🎯 Лучшие практики

### **Именование тестов:**
- `test[Scenario][ExpectedResult]`
- Пример: `testIdealWork52Minutes`, `testInsufficientActivity`

### **Структура теста:**
```swift
func testScenario() {
    // Arrange: Подготовка данных и состояния
    
    // Act: Выполнение тестируемого действия
    
    // Assert: Проверка результата
    XCTAssertTrue(condition, "Описательное сообщение об ошибке")
}
```

### **Mock объекты:**
- Используйте для изоляции тестируемого компонента
- Наследуйтесь от реальных классов
- Переопределяйте только необходимые методы

### **Сообщения об ошибках:**
- Должны быть описательными
- Включать ожидаемое и фактическое значение
- Помогать в диагностике проблемы

## 🔍 Отладка тестов

### **Просмотр детальных логов:**
```bash
xcodebuild test -project SimplePomodoroTest.xcodeproj -scheme SimplePomodoroTest -destination 'platform=macOS' -only-testing:SimplePomodoroTestTests/InformalSessionTests | grep -E "(Test|PASS|FAIL|Error)"
```

### **Запуск в отладочном режиме:**
- Откройте Xcode
- Выберите Test Navigator (⌘6)
- Нажмите на тест правой кнопкой → Debug

### **Добавление точек останова:**
- Поставьте breakpoint в тестовом методе
- Запустите тест в отладочном режиме
- Исследуйте состояние объектов

## 🎯 Модуль 2: PomodoroTimerTest

### **Описание**
Тестирует логику формальных интервалов (Pomodoro Timer) - состояния, переходы, паузы.

### **Покрываемые сценарии (5 тестов):**
1. **Запуск рабочего интервала** - проверка начала работы
2. **Переход на короткий перерыв** - логика перерывов
3. **Логика длинного перерыва** - каждый 4-й интервал
4. **Пауза и возобновление** - управление состоянием
5. **Остановка таймера** - сброс состояния

## ☕ Модуль 3: BreakSystemTest

### **Описание**
Тестирует систему отслеживания качества отдыха и перерывов.

### **Покрываемые сценарии (6 тестов):**
1. **Начало отслеживания** - активация системы
2. **Идеальный отдых** - 100% качества без активности
3. **Плохой отдых** - 0% качества при постоянной активности
4. **Средний отдых** - 50% качества при смешанной активности
5. **Остановка отслеживания** - деактивация системы
6. **Изоляция данных** - активность не записывается без отслеживания

## 📈 Метрики качества

### **Покрытие функциональности:**
- ✅ **Неформальные сессии:** 6 тестов (100% основных сценариев)
- ✅ **Формальные интервалы:** 5 тестов (100% состояний Pomodoro)
- ✅ **Система отдыха:** 6 тестов (100% отслеживания качества)
- 📋 **Планируется:** Детекция активности, сон/пробуждение, статистика

### **Качество тестов:**
- ✅ **Реальная логика:** Используют настоящие классы приложения
- ✅ **Найденные баги:** 2 реальные проблемы обнаружены и исправлены
- ✅ **Детерминированность:** 100% стабильные результаты
- ✅ **Скорость:** <1 секунды для всех 17 тестов

### **Интеграция в разработку:**
- ✅ **Автоматический запуск:** При каждой сборке через `./build.sh`
- ✅ **Остановка при ошибках:** Сборка прерывается если тесты провалены
- ✅ **Четкие отчеты:** Процент успешности и детали провалов
- ✅ **Защита от регрессий:** Реальная проверка качества кода

## 🎯 Планы развития

### **Приоритет 1: Расширение покрытия**
- [ ] **ActivityDetectionTest** - тестирование детекции активности мыши/клавиатуры
- [ ] **SleepWakeTest** - тестирование обработки сна/пробуждения системы
- [ ] **ProjectManagementTest** - тестирование системы проектов и задач
- [ ] **StatisticsTest** - тестирование сбора и анализа статистики

### **Приоритет 2: Углубление качества**
- [ ] **Интеграционные тесты** - взаимодействие между компонентами
- [ ] **Тесты производительности** - проверка скорости критических операций
- [ ] **Стресс-тесты** - поведение при больших объемах данных

### **Приоритет 3: Автоматизация**
- [ ] **CI/CD интеграция** - автоматический запуск при коммитах
- [ ] **Автоматические UI тесты** - проверка интерфейса
- [ ] **Регрессионное тестирование** - автоматическая проверка старых багов

### **⚠️ Принципы для всех новых тестов:**
- **Только реальная логика** - никаких упрощений
- **Максимальная близость к реальности** - тестируем как пользователь
- **Быстрота выполнения** - каждый тест <1 секунды
- **Четкие сообщения об ошибках** - понятно что сломалось и почему

## 🆕 Новые модули тестов (добавлены для исправления критической проблемы)

### **⭐ Модуль 4: Показ окна** (`Tests/WindowDisplayTest.swift`)
**Цель:** Тестирование показа окна неформальных сессий с использованием моков

**Компоненты:**
- `MockInformalSessionDetector` - мок детектора для изоляции логики
- `MockModernCompletionWindow` - мок окна для проверки вызовов

**Ключевые тесты:**
1. **🎯 Воспроизведение проблемы пользователя (43/52 активных минут)**
   - Заполняет точными данными: 8 неактивных + 43 активных + 1 неактивная
   - Проверяет что логика правильно определяет необходимость показа
   - Тестирует callback цепочку от детектора до окна

2. **🔧 Проверка настройки callback**
   - Проверяет что callback `onRestSuggestionNeeded` правильно настроен
   - Тестирует что callback вызывается при срабатывании условий

3. **🪟 Проверка создания и показа окна**
   - Тестирует что окно создается с правильным режимом (`.restSuggestion`)
   - Проверяет что окно показывается и позиционируется

4. **🎯 Граничные случаи**
   - Тестирует поведение на границе (41 vs 42 минуты)
   - Проверяет различные сценарии активности

### **⭐ Модуль 5: Интегрированные тесты окна** (`Tests/IntegratedWindowTest.swift`)
**Цель:** Тестирование с реальными компонентами приложения

**Особенности:**
- Работает с реальными объектами `AppDelegate` и `InformalSessionDetector`
- Использует рефлексию для безопасного взаимодействия с компонентами
- Может запускаться как в контексте приложения, так и автономно

**Функции:**
1. **`runIntegratedWindowTests()`** - полные интегрированные тесты
2. **`quickWindowTest()`** - быстрая проверка основных компонентов
3. **`runAutomatedWindowTests()`** - автоматические тесты для сборки

### **⭐ Модуль 6: Логика показа окна** (`Tests/WindowLogicTest.swift`) **[КРИТИЧЕСКИЙ]**
**Цель:** Тестирование исправления критической проблемы с показом окна

**Проблема:** Окно неформальных сессий не показывалось, несмотря на правильную работу детектора

**Исправление:** Изменена логика проверки видимости окна в `AppDelegate.showUnifiedReminder()`

**Ключевые тесты:**
1. **🔧 Исправленная логика: невидимое окно пересоздается**
   - Проверяет что невидимое окно правильно пересоздается
   - Тестирует новую логику проверки `windowExists && !windowVisible`

2. **👁️ Видимое окно не пересоздается**
   - Проверяет что видимое окно не пересоздается без необходимости
   - Оптимизация производительности

3. **🐛 Демонстрация проблемы старой логики**
   - Сравнивает старую и новую логику
   - Показывает как старая логика НЕ пересоздавала невидимое окно

4. **🎯 Воспроизведение проблемы пользователя**
   - Точная симуляция ситуации пользователя
   - Демонстрирует что исправление решает проблему

5. **🧠 Логика детектора: 43 активные минуты из 52**
   - Проверяет математику определения необходимости показа
   - Тестирует пороговые значения

6. **🎯 Граничные случаи детектора**
   - Тестирует поведение на границе (41 vs 42 минуты)
   - Проверяет точность логики

**Интеграция в сборку:**
- Автоматически запускается при `./build.sh`
- Провал любого теста останавливает сборку
- Защищает от регрессии критического исправления

---

## 🎯 **РЕЗЮМЕ: Главные правила тестирования uProd**

### **✅ ЧТО ДЕЛАТЬ:**
1. **ВСЕГДА начинай с реальных тестов** - они защищают от регрессий
2. **Добавляй интеграционные тесты** - они проверяют полный путь
3. **Mock-тесты только для алгоритмов** - и только в дополнение к реальным

### **❌ ЧТО НЕ ДЕЛАТЬ:**
1. **НЕ полагайся только на Mock-тесты** - они создают иллюзию безопасности
2. **НЕ тестируй только изолированную логику** - компоненты должны работать вместе
3. **НЕ создавай тесты ради тестов** - каждый тест должен находить реальные проблемы

### **🛡️ ЗАЩИТА ОТ РЕГРЕССИЙ:**
- **Реальные тесты** читают исходный код и ловят поломки ✅
- **Mock-тесты** тестируют свою логику и НЕ ловят поломки ❌
- **Интеграционные тесты** проверяют что компоненты работают вместе ✅

### **🚨 ПОМНИ:**
**Цель тестов - защитить от регрессий, а не показать зеленые галочки!**

---

**Последнее обновление:** 25 июля 2025
**Версия документации:** 3.1 (добавлены модули 5-6 для унифицированной системы)
**Автор:** uProd Auto-Testing System

## 🆕 Новые модули тестирования (25 июля 2025)

### **🧪 Модуль 5: UnifiedReminderSystemTest**
**Файл:** `Tests/UnifiedReminderSystemTest.swift`
**Описание:** Проверяет унификацию логики между формальными и неформальными окнами

**Тесты:**
1. **Унификация эскалации** - Формальные и неформальные интервалы используют одинаковую эскалацию
2. **Логика тряски** - Одинаковая логика тряски для всех типов окон
3. **Цветовая схема** - Единая цветовая схема статус-бара
4. **Непрерывность эскалации** - Эскалация работает без остановок
5. **Обновление статус-бара** - Правильное обновление при изменении уровня

### **🧪 Модуль 6: RealLogicIntegrationTest**
**Файл:** `Tests/RealLogicIntegrationTest.swift`
**Описание:** Интеграционные тесты реальной логики без моков

**Тесты:**
1. **OvertimeConfig эскалация** - Правильные уровни эскалации
2. **InformalSessionDetector логика** - Определение длинных сессий
3. **Cooldown система** - Работа системы cooldown
4. **Последовательность эскалации** - Правильная последовательность уровней
5. **Граничные значения** - Обработка границ между уровнями
6. **Производительность** - Быстрая работа OvertimeConfig
7. **Интеграция компонентов** - Совместная работа детектора и конфига

**Цель новых модулей:** Проверить что исправления логики тряски, эскалации и таймеров действительно унифицировали поведение формальных и неформальных окон.

### **⭐ Модуль 7: Новая единая система** (`Tests/UnifiedSystemTest.swift`)
**Цель:** Тестирование архитектуры единой системы напоминаний

**Ключевые тесты:**
- Правильность уровней эскалации OvertimeConfig
- Логика показа напоминаний только при изменении уровня
- Начало с уровня 0 (зеленая зона)
- Обновление статус-бара
- Интеграция с реальными компонентами

**Архитектурные улучшения:** Единая система для формальных и неформальных интервалов

### **⭐ Модуль 8: Поведение кнопки тест** (`Tests/TestButtonBehaviorTest.swift`)
**Цель:** Проверка правильного поведения кнопки "Тест" в настройках

**Ключевые тесты:**
- Тест начинается с уровня 0 (зеленая зона)
- Правильная последовательность уровней (0→1→2→3)
- НЕ начинаем сразу с желтой зоны
- Правильная прогрессия во время теста
- Соответствие цветов уровням

**Исправленные проблемы:** Тест больше не начинается сразу с желтой зоны

## 🎯 Заключение

Система тестирования uProd построена на принципе **"реальная логика прежде всего"**. Это обеспечивает максимальную защиту от регрессий и гарантирует, что тесты действительно проверяют работоспособность приложения.

**Помните:** Лучше иметь меньше тестов, но качественных, чем много поверхностных тестов, которые создают ложное ощущение безопасности.
