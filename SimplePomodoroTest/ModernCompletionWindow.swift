import Cocoa

/// Режимы работы окна
enum WindowMode {
    case sessionCompleted    // Завершение интервала
    case restSuggestion     // Предложение отдыха
}

/// Современное красивое окно завершения интервала с градиентными кнопками
/// Теперь поддерживает разные режимы работы
class ModernCompletionWindow: NSWindow {

    // Колбэки для действий
    var onComplete: (() -> Void)?
    var onExtend1: (() -> Void)?
    var onExtend5: (() -> Void)?

    // Колбэки для режима предложения отдыха
    var onTakeBreak: (() -> Void)?
    var onContinueWorking: (() -> Void)?

    // UI элементы для обновления
    private var titleLabel: NSTextField!
    private var subtitleLabel: NSTextField!
    private var reminderCount: Int = 0
    private var currentMode: WindowMode = .sessionCompleted

    // Кнопки (будут пересоздаваться в зависимости от режима)
    private var button1: NSButton!
    private var button3: NSButton!

    // Constraints для текста (будут обновляться в зависимости от режима)
    private var textConstraints: [NSLayoutConstraint] = []

    // Флаг для анимации тряски
    private var shouldShowShakeAnimation = false
    // Текущий уровень напоминания для выбора правильной анимации
    private var currentReminderLevel = 0
    
    override init(contentRect: NSRect, styleMask style: NSWindow.StyleMask, backing backingStoreType: NSWindow.BackingStoreType, defer flag: Bool) {
        super.init(contentRect: NSRect(x: 0, y: 0, width: 380, height: 100),
                  styleMask: [.borderless],
                  backing: .buffered,
                  defer: false)

        setupWindow()
        setupUI()
    }
    
    private func setupWindow() {
        print("🎨 ModernCompletionWindow: Настройка компактного окна")

        // Настройка окна
        self.isOpaque = false
        self.backgroundColor = NSColor.clear
        self.level = .floating
        self.hasShadow = true
        self.isMovableByWindowBackground = true

        // НЕ центрируем - позиционируем возле status item
    }

    func positionRelativeToStatusItem(statusItemFrame: NSRect) {
        logDebug("Window", "ModernCompletionWindow: Позиционирование возле status item")
        logDebug("Window", "ModernCompletionWindow: statusItemFrame = \(statusItemFrame)")

        let windowWidth = self.frame.width
        let windowHeight = self.frame.height
        logDebug("Window", "ModernCompletionWindow: windowSize = \(windowWidth) x \(windowHeight)")

        // Позиционируем под status item с небольшим отступом
        let x = statusItemFrame.midX - windowWidth / 2
        let y = statusItemFrame.minY - windowHeight - 8

        let newPosition = NSPoint(x: x, y: y)
        logInfo("Window", "ModernCompletionWindow: Новая позиция = \(newPosition)")

        self.setFrameOrigin(newPosition)
        logInfo("Window", "ModernCompletionWindow: Позиционирование завершено, итоговая позиция = \(self.frame.origin)")
    }

    // Переопределяем setFrameOrigin для отладки
    override func setFrameOrigin(_ point: NSPoint) {
        logInfo("Window", "ModernCompletionWindow: setFrameOrigin вызван с позицией \(point)")
        logInfo("Window", "ModernCompletionWindow: Стек вызовов: \(Thread.callStackSymbols.prefix(3))")
        super.setFrameOrigin(point)
    }

    /// Запускает анимацию тряски, если она нужна для текущего режима
    private func startShakeAnimationIfNeeded() {
        // ОТЛАДКА: Всегда логируем состояние
        logInfo("Window", "🔍 startShakeAnimationIfNeeded: shouldShowShakeAnimation=\(shouldShowShakeAnimation), currentReminderLevel=\(currentReminderLevel), currentMode=\(currentMode)")

        // Проверяем, нужна ли анимация тряски для текущего режима
        if shouldShowShakeAnimation {
            logInfo("Window", "startShakeAnimationIfNeeded: Запускаем анимацию тряски для уровня \(currentReminderLevel)")

            // Выбираем правильную анимацию в зависимости от уровня эскалации
            switch currentReminderLevel {
            case 1:
                // Желтый уровень - легкая тряска
                addLightShakeAnimation()
            case 2:
                // Оранжевый уровень - средняя тряска
                addMediumShakeAnimation()
            case 3:
                // Красный уровень - тряска + пульсация
                addShakeAnimation()
            case 4:
                // Темно-бордовый уровень - сильная тряска
                addStrongShakeAnimation()
            default:
                // Критический уровень - очень сильная тряска
                addCriticalShakeAnimation()
            }
        } else {
            logInfo("Window", "startShakeAnimationIfNeeded: Анимация тряски не нужна")
        }
    }

    // MARK: - Mode Configuration

    func configureForMode(_ mode: WindowMode) {
        currentMode = mode
        updateContentForMode()
    }

    private func updateContentForMode() {
        switch currentMode {
        case .sessionCompleted:
            titleLabel.stringValue = "🎉 Session completed!"
            subtitleLabel.stringValue = "What's next?"
            setupSessionCompletedButtons()
            setupTextConstraintsForSessionCompleted()

        case .restSuggestion:
            titleLabel.stringValue = "🌿 Time for rest"
            subtitleLabel.stringValue = "Using laptop 52+ minutes"
            setupRestSuggestionButtons()
            setupTextConstraintsForRestSuggestion()
        }
    }

    private func setupSessionCompletedButtons() {
        // Удаляем старые кнопки
        removeExistingButtons()

        // Создаем кнопки для завершения сессии
        let softYellow = NSColor(red: 0.8, green: 0.75, blue: 0.5, alpha: 1.0)
        let softPurple = NSColor(red: 0.7, green: 0.6, blue: 0.8, alpha: 1.0)

        button1 = createDashedButton(title: "I need a couple of minutes", color: softYellow)
        button1.target = self
        button1.action = #selector(extend1Clicked)

        button3 = createGradientButton(title: "Take a break", color: .green, isSmall: false)
        button3.target = self
        button3.action = #selector(completeClicked)

        addButtonsToContainer()
    }

    private func setupRestSuggestionButtons() {
        // Удаляем старые кнопки
        removeExistingButtons()

        // Создаем кнопки для предложения отдыха
        let softOrange = NSColor(red: 0.9, green: 0.7, blue: 0.4, alpha: 1.0)

        // Главная кнопка "Take a break" - зеленая градиентная
        button3 = createGradientButton(title: "Take a break", color: .green, isSmall: false)
        button3.target = self
        button3.action = #selector(takeBreakClicked)

        // Альтернативная кнопка "Just a few minutes" - в стиле "+1 min" (без фона, с обводкой)
        let softPurple = NSColor(red: 0.7, green: 0.6, blue: 0.8, alpha: 1.0)
        button1 = createDashedButton(title: "Just a few minutes", color: softPurple)
        button1.target = self
        button1.action = #selector(fewMinutesClicked)

        addButtonsToContainer()
    }

    private func removeExistingButtons() {
        button1?.removeFromSuperview()
        button3?.removeFromSuperview()
    }

    private func addButtonsToContainer() {
        guard let containerView = self.contentView else { return }

        containerView.addSubview(button1)
        if let button3 = button3 {
            containerView.addSubview(button3)
        }

        setupButtonConstraints()
    }

    private func setupButtonConstraints() {
        guard let containerView = self.contentView else { return }

        switch currentMode {
        case .sessionCompleted:
            // Оригинальная компоновка: Take a break слева, +1/+5 справа
            NSLayoutConstraint.activate([
                // Главная кнопка Take a break (слева)
                button3.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 16),
                button3.leadingAnchor.constraint(equalTo: containerView.centerXAnchor, constant: 20),
                button3.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -15),
                button3.heightAnchor.constraint(equalToConstant: 32),

                // Кнопка "I need a couple of minutes" (снизу) - растягивается по ширине
                button1.topAnchor.constraint(equalTo: button3.bottomAnchor, constant: 8),
                button1.leadingAnchor.constraint(equalTo: button3.leadingAnchor),
                button1.trailingAnchor.constraint(equalTo: button3.trailingAnchor),
                button1.heightAnchor.constraint(equalToConstant: 20),
                button1.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -16)
            ])

        case .restSuggestion:
            // Кнопки друг под другом
            NSLayoutConstraint.activate([
                // Главная кнопка Take a break (сверху) - градиентная, большая
                button3.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 16),
                button3.leadingAnchor.constraint(equalTo: containerView.centerXAnchor, constant: 20),
                button3.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -15),
                button3.heightAnchor.constraint(equalToConstant: 32),

                // Альтернативная кнопка "Just a few minutes" (снизу) - пунктирная, маленькая
                button1.topAnchor.constraint(equalTo: button3.bottomAnchor, constant: 8),
                button1.leadingAnchor.constraint(equalTo: button3.leadingAnchor),
                button1.trailingAnchor.constraint(equalTo: button3.trailingAnchor),
                button1.heightAnchor.constraint(equalToConstant: 20), // Меньше чем главная кнопка

                containerView.bottomAnchor.constraint(equalTo: button1.bottomAnchor, constant: 16)
            ])
        }
    }

    private func setupTextConstraintsForSessionCompleted() {
        guard let containerView = self.contentView else { return }

        // Удаляем старые constraints
        NSLayoutConstraint.deactivate(textConstraints)
        textConstraints.removeAll()

        // Оригинальное позиционирование для завершения сессии
        textConstraints = [
            titleLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 25),
            titleLabel.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 25),
            titleLabel.widthAnchor.constraint(lessThanOrEqualToConstant: 160),

            subtitleLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 25),
            subtitleLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 8),
            subtitleLabel.widthAnchor.constraint(lessThanOrEqualToConstant: 160)
        ]

        NSLayoutConstraint.activate(textConstraints)
    }

    private func setupTextConstraintsForRestSuggestion() {
        guard let containerView = self.contentView else { return }

        // Удаляем старые constraints
        NSLayoutConstraint.deactivate(textConstraints)
        textConstraints.removeAll()

        // Точно такое же позиционирование, как для завершения сессии
        textConstraints = [
            titleLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 25),
            titleLabel.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 25),
            titleLabel.widthAnchor.constraint(lessThanOrEqualToConstant: 160),

            subtitleLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 25),
            subtitleLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 8),
            subtitleLabel.widthAnchor.constraint(lessThanOrEqualToConstant: 160)
        ]

        NSLayoutConstraint.activate(textConstraints)
    }


    
    private func setupUI() {
        print("🎨 ModernCompletionWindow: Создание компактного UI")

        // Создаем основной контейнер
        let containerView = NSView()
        containerView.wantsLayer = true
        containerView.translatesAutoresizingMaskIntoConstraints = false

        // Создаем сложный фон с несколькими радиальными градиентами
        setupComplexBackground(for: containerView)

        // Заголовок (слева вверху)
        titleLabel = NSTextField(labelWithString: "🎉 Session completed!")
        titleLabel.font = NSFont.systemFont(ofSize: 15, weight: .bold)
        titleLabel.textColor = NSColor.white
        titleLabel.alignment = .left
        titleLabel.translatesAutoresizingMaskIntoConstraints = false

        // Подзаголовок (слева внизу)
        subtitleLabel = NSTextField(labelWithString: "What's next?")
        subtitleLabel.font = NSFont.systemFont(ofSize: 13, weight: .medium)
        subtitleLabel.textColor = NSColor.white.withAlphaComponent(0.8)
        subtitleLabel.alignment = .left
        subtitleLabel.translatesAutoresizingMaskIntoConstraints = false

        // Добавляем базовые элементы
        containerView.addSubview(titleLabel)
        containerView.addSubview(subtitleLabel)

        self.contentView = containerView

        // Настраиваем режим по умолчанию (constraints будут установлены в configureForMode)
        configureForMode(.sessionCompleted)

        print("🎨 ModernCompletionWindow: UI создан")
    }
    
    private func setupComplexBackground(for view: NSView) {
        // Основной градиентный слой
        let mainGradient = CAGradientLayer()
        mainGradient.colors = [
            NSColor(red: 0.1, green: 0.1, blue: 0.15, alpha: 0.95).cgColor,
            NSColor(red: 0.15, green: 0.15, blue: 0.2, alpha: 0.95).cgColor
        ]
        mainGradient.startPoint = CGPoint(x: 0, y: 0)
        mainGradient.endPoint = CGPoint(x: 1, y: 1)
        mainGradient.cornerRadius = 12

        // Тень
        mainGradient.shadowColor = NSColor.black.cgColor
        mainGradient.shadowOpacity = 0.3
        mainGradient.shadowOffset = CGSize(width: 0, height: 4)
        mainGradient.shadowRadius = 12

        view.layer = mainGradient

        // Добавляем радиальные градиенты для красоты
        DispatchQueue.main.async {
            self.addRadialGradients(to: view)
        }
    }

    private func addRadialGradients(to view: NSView) {
        guard let layer = view.layer else { return }

        // Первый радиальный градиент (левый верхний)
        let radial1 = CAGradientLayer()
        radial1.type = .radial
        radial1.colors = [
            NSColor(red: 0.3, green: 0.4, blue: 0.8, alpha: 0.3).cgColor,
            NSColor.clear.cgColor
        ]
        radial1.startPoint = CGPoint(x: 0.2, y: 0.8)
        radial1.endPoint = CGPoint(x: 0.8, y: 0.2)
        radial1.frame = layer.bounds
        layer.addSublayer(radial1)

        // Второй радиальный градиент (правый нижний)
        let radial2 = CAGradientLayer()
        radial2.type = .radial
        radial2.colors = [
            NSColor(red: 0.8, green: 0.3, blue: 0.6, alpha: 0.2).cgColor,
            NSColor.clear.cgColor
        ]
        radial2.startPoint = CGPoint(x: 0.8, y: 0.2)
        radial2.endPoint = CGPoint(x: 0.2, y: 0.8)
        radial2.frame = layer.bounds
        layer.addSublayer(radial2)
    }

    private func createGradientButton(title: String, color: ButtonColor, isSmall: Bool = false) -> NSButton {
        let button = NSButton(title: title, target: nil, action: nil)
        button.translatesAutoresizingMaskIntoConstraints = false
        button.isBordered = false
        button.wantsLayer = true
        
        // Создаем градиентный слой
        let gradientLayer = CAGradientLayer()
        
        switch color {
        case .green:
            gradientLayer.colors = [
                NSColor(red: 0.2, green: 0.8, blue: 0.3, alpha: 1.0).cgColor,
                NSColor(red: 0.1, green: 0.6, blue: 0.2, alpha: 1.0).cgColor
            ]
        case .blue:
            gradientLayer.colors = [
                NSColor(red: 0.3, green: 0.6, blue: 1.0, alpha: 1.0).cgColor,
                NSColor(red: 0.2, green: 0.4, blue: 0.8, alpha: 1.0).cgColor
            ]
        case .purple:
            gradientLayer.colors = [
                NSColor(red: 0.7, green: 0.3, blue: 1.0, alpha: 1.0).cgColor,
                NSColor(red: 0.5, green: 0.2, blue: 0.8, alpha: 1.0).cgColor
            ]
        case .darkGray:
            gradientLayer.colors = [
                NSColor(red: 0.4, green: 0.4, blue: 0.4, alpha: 1.0).cgColor,
                NSColor(red: 0.25, green: 0.25, blue: 0.25, alpha: 1.0).cgColor
            ]
        case .orange:
            gradientLayer.colors = [
                NSColor(red: 1.0, green: 0.6, blue: 0.2, alpha: 1.0).cgColor,
                NSColor(red: 0.9, green: 0.4, blue: 0.1, alpha: 1.0).cgColor
            ]
        }
        
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 0, y: 1)
        gradientLayer.cornerRadius = isSmall ? 6 : 8

        // Тень для кнопки
        gradientLayer.shadowColor = NSColor.black.cgColor
        gradientLayer.shadowOpacity = 0.2
        gradientLayer.shadowOffset = CGSize(width: 0, height: 1)
        gradientLayer.shadowRadius = 3

        button.layer = gradientLayer

        // Белый текст
        let fontSize: CGFloat = isSmall ? 11 : 13
        button.attributedTitle = NSAttributedString(
            string: title,
            attributes: [
                .foregroundColor: NSColor.white,
                .font: NSFont.systemFont(ofSize: fontSize, weight: .semibold)
            ]
        )
        
        return button
    }

    private func createDashedButton(title: String, color: NSColor) -> NSButton {
        let button = NSButton(title: title, target: nil, action: nil)
        button.translatesAutoresizingMaskIntoConstraints = false
        button.isBordered = false
        button.wantsLayer = true

        // Создаем основной слой
        let containerLayer = CALayer()
        containerLayer.backgroundColor = NSColor.clear.cgColor
        button.layer = containerLayer

        // Создаем слой с пунктирной обводкой
        let borderLayer = CAShapeLayer()
        borderLayer.fillColor = NSColor.clear.cgColor
        borderLayer.strokeColor = color.withAlphaComponent(0.7).cgColor
        borderLayer.lineWidth = 1.0
        borderLayer.lineDashPattern = [3, 2] // Пунктирная линия: 3px линия, 2px пробел

        containerLayer.addSublayer(borderLayer)

        // Обновляем путь при изменении размера
        DispatchQueue.main.async {
            let bounds = button.bounds
            let path = NSBezierPath(roundedRect: bounds, xRadius: 6, yRadius: 6)
            borderLayer.path = path.cgPath
            borderLayer.frame = bounds
        }

        // Цветной текст
        button.attributedTitle = NSAttributedString(
            string: title,
            attributes: [
                .foregroundColor: color.withAlphaComponent(0.8),
                .font: NSFont.systemFont(ofSize: 11, weight: .medium)
            ]
        )

        return button
    }

    enum ButtonColor {
        case green, blue, purple, darkGray, orange
    }
    
    // MARK: - Actions
    @objc private func extend1Clicked() {
        print("🎨 ModernCompletionWindow: +1 мин нажата")
        hideWithAnimation {
            self.onExtend1?()
        }
    }
    
    @objc private func extend5Clicked() {
        print("🎨 ModernCompletionWindow: +5 мин нажата")
        hideWithAnimation {
            self.onExtend5?()
        }
    }
    
    @objc private func completeClicked() {
        print("🎨 ModernCompletionWindow: Завершить нажата")
        hideWithAnimation {
            self.onComplete?()
        }
    }

    @objc private func takeBreakClicked() {
        print("🌿 ModernCompletionWindow: Take break нажата")
        hideWithAnimation {
            self.onTakeBreak?()
        }
    }

    @objc private func continueWorkingClicked() {
        print("🌿 ModernCompletionWindow: Continue нажата")
        hideWithAnimation {
            self.onContinueWorking?()
        }
    }

    @objc private func fewMinutesClicked() {
        print("🌿 ModernCompletionWindow: Just a few minutes нажата")
        hideWithAnimation {
            self.onContinueWorking?() // Пока используем тот же callback
        }
    }



    // MARK: - Message Updates
    func updateMessage(reminderCount: Int) {
        print("🎨 ModernCompletionWindow: Обновление сообщения, цветовой уровень #\(reminderCount)")
        self.reminderCount = reminderCount
        self.currentReminderLevel = reminderCount

        // Обновляем текст в зависимости от цветового уровня переработки
        let (title, subtitle) = getMessageText(for: reminderCount)
        titleLabel.stringValue = title
        subtitleLabel.stringValue = subtitle

        // Устанавливаем флаг анимации в зависимости от цветового уровня
        switch reminderCount {
        case 0:
            // Уровень 0 - без анимации
            shouldShowShakeAnimation = false
        case 1:
            // Желтый уровень - легкая тряска
            shouldShowShakeAnimation = true
        case 2:
            // Оранжевый уровень - средняя тряска
            shouldShowShakeAnimation = true
        case 3:
            // Красный уровень - тряска + пульсация
            shouldShowShakeAnimation = true
        case 4:
            // Темно-бордовый уровень - сильная тряска
            shouldShowShakeAnimation = true
        default:
            // Критический уровень - очень сильная тряска
            shouldShowShakeAnimation = true
        }

        logInfo("Window", "updateMessage: reminderCount=\(reminderCount), shouldShowShakeAnimation=\(shouldShowShakeAnimation)")

        // ВАЖНО: Если окно уже видимо, запускаем анимацию тряски сразу
        if self.isVisible && shouldShowShakeAnimation {
            logInfo("Window", "updateMessage: Окно уже открыто, запускаем анимацию тряски")
            startShakeAnimationIfNeeded()
        }
    }

    /// Обновляет сообщение БЕЗ анимации тряски (для новых окон)
    func updateMessageWithoutShake(reminderCount: Int) {
        print("🎨 ModernCompletionWindow: Обновление сообщения БЕЗ тряски, цветовой уровень #\(reminderCount)")
        self.reminderCount = reminderCount
        self.currentReminderLevel = reminderCount

        // Обновляем текст в зависимости от цветового уровня переработки
        let (title, subtitle) = getMessageText(for: reminderCount)
        titleLabel.stringValue = title
        subtitleLabel.stringValue = subtitle

        // ВАЖНО: Для новых окон ВСЕГДА отключаем тряску, независимо от уровня
        shouldShowShakeAnimation = false

        logInfo("Window", "updateMessageWithoutShake: reminderCount=\(reminderCount), shouldShowShakeAnimation=\(shouldShowShakeAnimation) - тряска ПРИНУДИТЕЛЬНО отключена для нового окна")
    }

    private func getMessageText(for colorLevel: Int) -> (title: String, subtitle: String) {
        let level = OvertimeConfig.levels.first { $0.level == colorLevel } ?? OvertimeConfig.levels.last!
        return (level.title, level.subtitle)
    }

    private func addPulseAnimation() {
        guard let contentView = self.contentView else { return }
        contentView.wantsLayer = true
        let animation = CABasicAnimation(keyPath: "transform.scale")
        animation.fromValue = 1.0
        animation.toValue = 1.03
        animation.duration = 0.8
        animation.autoreverses = true
        animation.repeatCount = 2
        contentView.layer?.add(animation, forKey: "pulse")
    }

    private func addStrongPulseAnimation() {
        guard let contentView = self.contentView else { return }
        contentView.wantsLayer = true
        let animation = CABasicAnimation(keyPath: "transform.scale")
        animation.fromValue = 1.0
        animation.toValue = 1.06
        animation.duration = 0.6
        animation.autoreverses = true
        animation.repeatCount = 3
        contentView.layer?.add(animation, forKey: "strongPulse")
    }

    private func addPulseAnimationToContainer() {
        guard let contentView = self.contentView,
              let containerView = contentView.subviews.first else { return }
        containerView.wantsLayer = true
        let animation = CABasicAnimation(keyPath: "transform.scale")
        animation.fromValue = 1.0
        animation.toValue = 1.03
        animation.duration = 0.8
        animation.autoreverses = true
        animation.repeatCount = 2
        containerView.layer?.add(animation, forKey: "containerPulse")
    }

    private func addLightShakeAnimation() {
        // Сохраняем текущую позицию окна
        let originalFrame = self.frame
        let originalOrigin = NSPoint(x: originalFrame.origin.x, y: originalFrame.origin.y)
        logInfo("Window", "addLightShakeAnimation: Захваченная позиция = \(originalOrigin)")
        let shakeValues: [CGFloat] = [0, -4, 4, -3, 3, -2, 2, -1, 1, 0] // Увеличили интенсивность

        var index = 0
        Timer.scheduledTimer(withTimeInterval: 0.05, repeats: true) { [weak self] timer in
            guard let self = self else {
                timer.invalidate()
                return
            }

            if index < shakeValues.count {
                let offsetX = shakeValues[index]
                let newOrigin = NSPoint(x: originalOrigin.x + offsetX, y: originalOrigin.y)
                self.setFrameOrigin(newOrigin)
                index += 1
            } else {
                timer.invalidate()
                // Возвращаем в исходную позицию
                self.setFrameOrigin(originalOrigin)
            }
        }
    }

    private func addMediumShakeAnimation() {
        // Сохраняем текущую позицию окна
        let originalFrame = self.frame
        let originalOrigin = NSPoint(x: originalFrame.origin.x, y: originalFrame.origin.y)
        let shakeValues: [CGFloat] = [0, -6, 6, -5, 5, -4, 4, -3, 3, -2, 2, -1, 1, 0] // Более интенсивная

        var index = 0
        Timer.scheduledTimer(withTimeInterval: 0.04, repeats: true) { [weak self] timer in // Быстрее
            guard let self = self else {
                timer.invalidate()
                return
            }

            if index < shakeValues.count {
                let offsetX = shakeValues[index]
                let newOrigin = NSPoint(x: originalOrigin.x + offsetX, y: originalOrigin.y)
                self.setFrameOrigin(newOrigin)
                index += 1
            } else {
                timer.invalidate()
                // Возвращаем в исходную позицию
                self.setFrameOrigin(originalOrigin)
            }
        }
    }

    private func addShakeAnimation() {
        // Сохраняем текущую позицию окна
        let originalFrame = self.frame
        let originalOrigin = NSPoint(x: originalFrame.origin.x, y: originalFrame.origin.y)
        let shakeValues: [CGFloat] = [0, -8, 8, -7, 7, -6, 6, -5, 5, -4, 4, -3, 3, -2, 2, -1, 1, 0] // Еще более интенсивная

        var index = 0
        Timer.scheduledTimer(withTimeInterval: 0.03, repeats: true) { [weak self] timer in // Еще быстрее
            guard let self = self else {
                timer.invalidate()
                return
            }

            if index < shakeValues.count {
                let offsetX = shakeValues[index]
                let newOrigin = NSPoint(x: originalOrigin.x + offsetX, y: originalOrigin.y)
                self.setFrameOrigin(newOrigin)
                index += 1
            } else {
                timer.invalidate()
                // Возвращаем в исходную позицию
                self.setFrameOrigin(originalOrigin)
            }
        }
    }

    private func addStrongShakeAnimation() {
        // Сохраняем текущую позицию окна
        let originalFrame = self.frame
        let originalOrigin = NSPoint(x: originalFrame.origin.x, y: originalFrame.origin.y)
        let shakeValues: [CGFloat] = [0, -12, 12, -10, 10, -8, 8, -6, 6, -4, 4, -2, 2, 0] // Сильная тряска

        var index = 0
        var repeatCount = 0
        let maxRepeats = 2

        func startShake() {
            Timer.scheduledTimer(withTimeInterval: 0.025, repeats: true) { [weak self] timer in // Очень быстро
                guard let self = self else {
                    timer.invalidate()
                    return
                }

                if index < shakeValues.count {
                    let offsetX = shakeValues[index]
                    let newOrigin = NSPoint(x: originalOrigin.x + offsetX, y: originalOrigin.y)
                    self.setFrameOrigin(newOrigin)
                    index += 1
                } else {
                    timer.invalidate()
                    repeatCount += 1
                    if repeatCount < maxRepeats {
                        index = 0
                        startShake() // Повторяем
                    } else {
                        // Возвращаем в исходную позицию
                        self.setFrameOrigin(originalOrigin)
                    }
                }
            }
        }
        startShake()
    }

    private func addCriticalShakeAnimation() {
        guard let contentView = self.contentView else { return }
        contentView.wantsLayer = true

        // Горизонтальная тряска
        let shakeX = CAKeyframeAnimation(keyPath: "transform.translation.x")
        shakeX.values = [0, -10, 10, -8, 8, -6, 6, -4, 4, -2, 2, 0]
        shakeX.duration = 0.8
        shakeX.repeatCount = 4

        // Вертикальная тряска
        let shakeY = CAKeyframeAnimation(keyPath: "transform.translation.y")
        shakeY.values = [0, -3, 3, -2, 2, -1, 1, 0]
        shakeY.duration = 0.4
        shakeY.repeatCount = 8

        contentView.layer?.add(shakeX, forKey: "criticalShakeX")
        contentView.layer?.add(shakeY, forKey: "criticalShakeY")

        addStrongPulseAnimation()
    }

    // MARK: - Animations
    func showWithAnimation() {
        print("🎨 ModernCompletionWindow: Показ с анимацией (старый метод)")

        // Начальное состояние
        self.alphaValue = 0
        self.setFrame(NSRect(x: self.frame.origin.x, y: self.frame.origin.y - 20, width: self.frame.width, height: self.frame.height), display: true)

        self.makeKeyAndOrderFront(nil)

        // Анимация появления
        NSAnimationContext.runAnimationGroup { context in
            context.duration = 0.4
            context.timingFunction = CAMediaTimingFunction(name: .easeOut)

            self.animator().alphaValue = 1
            self.animator().setFrame(NSRect(x: self.frame.origin.x, y: self.frame.origin.y + 20, width: self.frame.width, height: self.frame.height), display: true)
        }
    }

    /// Новый метод анимации появления (БЕЗ изменения позиции)
    func showAppearanceAnimation() {
        logInfo("Window", "ModernCompletionWindow: Анимация появления (новый метод)")
        logInfo("Window", "ModernCompletionWindow: Текущая позиция перед показом: \(self.frame.origin)")

        // НЕ МЕНЯЕМ позицию! Только прозрачность
        self.alphaValue = 0

        // Показываем окно
        self.makeKeyAndOrderFront(nil)
        logInfo("Window", "ModernCompletionWindow: Окно показано, позиция: \(self.frame.origin)")

        // Анимация появления - только прозрачность
        NSAnimationContext.runAnimationGroup { context in
            context.duration = 0.3
            context.timingFunction = CAMediaTimingFunction(name: .easeOut)

            self.animator().alphaValue = 1
            // НЕ МЕНЯЕМ ПОЗИЦИЮ! Окно уже правильно позиционировано
        } completionHandler: {
            logInfo("Window", "ModernCompletionWindow: Анимация завершена, позиция: \(self.frame.origin)")
            // ЗАПУСКАЕМ АНИМАЦИЮ ТРЯСКИ ТОЛЬКО ПОСЛЕ ЗАВЕРШЕНИЯ АНИМАЦИИ ПОЯВЛЕНИЯ
            self.startShakeAnimationIfNeeded()
        }
    }
    
    private func hideWithAnimation(completion: @escaping () -> Void) {
        print("🎨 ModernCompletionWindow: Скрытие с анимацией")
        
        NSAnimationContext.runAnimationGroup({ context in
            context.duration = 0.3
            context.timingFunction = CAMediaTimingFunction(name: .easeIn)
            
            self.animator().alphaValue = 0
            self.animator().setFrame(NSRect(x: self.frame.origin.x, y: self.frame.origin.y + 15, width: self.frame.width, height: self.frame.height), display: true)
        }) {
            self.orderOut(nil)
            completion()
        }
    }
}
