#!/bin/bash

#
# test.sh - Автоматическое тестирование uProd
#
# Запускает все юнит-тесты и интеграционные тесты
# Возвращает код ошибки если тесты провалены
#

set -e  # Остановка при первой ошибке

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧪 uProd Auto-Testing System${NC}"
echo -e "${BLUE}================================${NC}"
echo ""

# Проверяем наличие Swift
if ! command -v swift &> /dev/null; then
    echo -e "${RED}❌ Swift не найден! Установите Xcode Command Line Tools.${NC}"
    exit 1
fi

# Переходим в директорию проекта
cd "$(dirname "$0")"

echo -e "${YELLOW}📂 Рабочая директория: $(pwd)${NC}"
echo ""

# Счетчики тестов
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Функция запуска теста
run_test() {
    local test_name="$1"
    local test_file="$2"
    
    echo -e "${BLUE}🔍 Запуск: $test_name${NC}"
    echo "   Файл: $test_file"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    # Проверяем существование файла
    if [ ! -f "$test_file" ]; then
        echo -e "${RED}❌ Файл теста не найден: $test_file${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
    
    # Запускаем тест
    if swift "$test_file"; then
        echo -e "${GREEN}✅ $test_name: ПРОШЕЛ${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        echo -e "${RED}❌ $test_name: ПРОВАЛЕН${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# Запускаем тесты
echo -e "${YELLOW}🚀 Запуск юнит-тестов...${NC}"
echo ""

# Модуль 1: Система неформальных сессий
run_test "Модуль 1: Неформальные сессии" "Tests/InformalSessionTest.swift"

# Модуль 2: Формальные интервалы (Pomodoro)
run_test "Модуль 2: Формальные интервалы" "Tests/PomodoroTimerTest.swift"

# Модуль 3: Система отдыха
run_test "Модуль 3: Система отдыха" "Tests/BreakSystemTest.swift"

# Модуль 4: ТЕСТ ЛОГИКИ ПОКАЗА ОКНА (защита от регрессий)
run_test "Модуль 4: Логика показа окна" "Tests/RealAppDelegateLogicTest.swift"

# Модуль 5: УНИФИЦИРОВАННАЯ СИСТЕМА НАПОМИНАНИЙ (новый)
run_test "Модуль 5: Унифицированная система" "Tests/UnifiedReminderSystemTest.swift"

# Модуль 6: ИНТЕГРАЦИОННЫЕ ТЕСТЫ РЕАЛЬНОЙ ЛОГИКИ (новый)
run_test "Модуль 6: Интеграция реальной логики" "Tests/RealLogicIntegrationTest.swift"

# Модуль 7: НОВАЯ ЕДИНАЯ СИСТЕМА НАПОМИНАНИЙ (новый)
run_test "Модуль 7: Новая единая система" "Tests/UnifiedSystemTest.swift"

# Модуль 8: ПОВЕДЕНИЕ КНОПКИ ТЕСТ (новый)
run_test "Модуль 8: Поведение кнопки тест" "Tests/TestButtonBehaviorTest.swift"

echo ""
echo -e "${BLUE}================================${NC}"
echo -e "${BLUE}📊 ИТОГОВЫЕ РЕЗУЛЬТАТЫ${NC}"
echo -e "${BLUE}================================${NC}"
echo -e "📈 Всего тестов: $TOTAL_TESTS"
echo -e "✅ Прошло: $PASSED_TESTS"
echo -e "❌ Провалено: $FAILED_TESTS"

if [ $FAILED_TESTS -eq 0 ]; then
    SUCCESS_RATE=100
else
    SUCCESS_RATE=$((PASSED_TESTS * 100 / TOTAL_TESTS))
fi

echo -e "📊 Успешность: ${SUCCESS_RATE}%"
echo ""

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "${GREEN}🎉 ВСЕ ТЕСТЫ ПРОШЛИ!${NC}"
    echo -e "${GREEN}✅ Система готова к сборке.${NC}"
    exit 0
else
    echo -e "${RED}⚠️ ЕСТЬ ПРОБЛЕМЫ!${NC}"
    echo -e "${RED}❌ Сборка должна быть остановлена.${NC}"
    echo ""
    echo -e "${YELLOW}💡 Исправьте провалившиеся тесты перед продолжением.${NC}"
    exit 1
fi
